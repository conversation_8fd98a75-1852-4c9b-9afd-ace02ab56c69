<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Browse our comprehensive range of high-quality pharmaceutical tablets and capsules. EDEN Pharmaceuticals offers a wide selection of medications.">
    <title>Anti-Cold, Anti-Histaminic and Anti-Allergic - EDEN Pharmaceuticals</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Optimized CSS -->
    <link rel="stylesheet" href="../css/optimized.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Custom page styles -->
    <style>
        /* Banner styles */
        .products-hero {
            background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80');
        }

        /* Download section styles */
        .download-section {
            background-color: #f9fbfd;
            padding: 3rem 0;
            text-align: center;
        }

        .download-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .download-content h2 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .download-content p {
            margin-bottom: 2rem;
            color: #555;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            background-color: #0056b3;
            color: white;
            padding: 1rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .download-btn:hover {
            background-color: #003d7a;
            transform: translateY(-2px);
        }

        .download-btn i {
            font-size: 1.2rem;
        }

        /* Mobile-specific download section */
        @media (max-width: 768px) {
            .download-section {
                padding: 2rem 0;
            }

            .download-content h2 {
                font-size: 1.5rem;
            }

            .download-content p {
                font-size: 0.9rem;
                margin-bottom: 1.5rem;
            }

            .download-btn {
                padding: 0.875rem 1.75rem;
                margin: 0.5rem auto;
                font-size: 0.95rem;
                gap: 0.6rem;
                min-height: 48px;
                border-radius: 8px;
                touch-action: manipulation;
                box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
            }
        }

        @media (max-width: 480px) {
            .download-section {
                padding: 1.5rem 0;
            }

            .download-content {
                padding: 0 0.5rem;
            }

            .download-content h2 {
                font-size: 1.3rem;
            }

            .download-btn {
                padding: 1rem 2rem;
                margin: 0.75rem auto;
                font-size: 1rem;
                width: 100%;
                max-width: 280px;
                min-height: 52px;
                border-radius: 10px;
                touch-action: manipulation;
                box-shadow: 0 6px 16px rgba(0, 86, 179, 0.25);
            }
        }

        /* Lightbox styles */
        .product-image {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .product-image::after {
            content: "\f00e";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-image:hover::after {
            opacity: 1;
        }

        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            padding: 20px;
            box-sizing: border-box;
        }

        .lightbox.active {
            opacity: 1;
            visibility: visible;
        }

        .lightbox-content {
            position: relative;
            max-width: 100%;
            max-height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .lightbox-image {
            display: block;
            max-width: 100%;
            max-height: calc(100vh - 120px);
            width: auto;
            height: auto;
            object-fit: contain;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
            border-radius: 8px;
        }

        .lightbox-close {
            position: absolute;
            top: -50px;
            right: 0;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .lightbox-close:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
        }

        .lightbox-caption {
            position: absolute;
            bottom: -60px;
            left: 0;
            right: 0;
            color: white;
            text-align: center;
            font-size: 16px;
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            margin: 0 20px;
        }

        /* Mobile-specific lightbox styles */
        @media (max-width: 768px) {
            .lightbox {
                padding: 10px;
            }

            .lightbox-image {
                max-height: calc(100vh - 100px);
            }

            .lightbox-close {
                top: -45px;
                right: -5px;
                width: 35px;
                height: 35px;
                font-size: 18px;
            }

            .lightbox-caption {
                bottom: -50px;
                font-size: 14px;
                padding: 10px 15px;
                margin: 0 10px;
            }
        }

        @media (max-width: 480px) {
            .lightbox {
                padding: 5px;
            }

            .lightbox-image {
                max-height: calc(100vh - 80px);
            }

            .lightbox-close {
                top: -40px;
                right: -5px;
                width: 30px;
                height: 30px;
                font-size: 16px;
            }

            .lightbox-caption {
                bottom: -45px;
                font-size: 12px;
                padding: 8px 12px;
                margin: 0 5px;
            }
        }

        /* Fix navigation visibility and contact button issues */
        @media (min-width: 769px) {
            .nav-links {
                position: static !important;
                left: auto !important;
                top: auto !important;
                width: auto !important;
                height: auto !important;
                background: transparent !important;
                flex-direction: row !important;
                padding: 0 !important;
                transition: none !important;
                overflow: visible !important;
                z-index: auto !important;
                box-shadow: none !important;
                gap: 1.5rem !important;
                border-top: none !important;
                display: flex !important;
            }

            .nav-links li {
                margin: 0 !important;
                width: auto !important;
            }

            .nav-links a {
                display: inline-block !important;
                padding: 0.6rem 1.2rem !important;
                border-radius: 4px !important;
                width: auto !important;
                color: #2c3e50 !important;
                font-weight: 500 !important;
            }

            /* Contact button styling */
            .contact-btn {
                display: inline-flex !important;
                background: #3498db !important;
                color: white !important;
                padding: 0.6rem 1.2rem !important;
                margin: 0.25rem !important;
                border-radius: 6px !important;
                min-height: 44px !important;
                align-items: center !important;
                justify-content: center !important;
                text-decoration: none !important;
                font-weight: 500 !important;
                border: 2px solid #3498db !important;
                transition: all 0.3s ease !important;
                cursor: pointer !important;
                box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2) !important;
            }

            .contact-btn:hover {
                background: #2980b9 !important;
                border-color: #2980b9 !important;
                color: white !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3) !important;
                text-decoration: none !important;
            }

            .contact-btn:visited,
            .contact-btn:active,
            .contact-btn:focus {
                color: white !important;
                text-decoration: none !important;
                background: #3498db !important;
                border-color: #3498db !important;
            }
        }

        @media (max-width: 1200px) and (min-width: 769px) {
            .nav-links {
                gap: 1rem !important;
            }
        }

        /* Footer Dropdown Menu Styles */
        .footer-dropdown {
            position: relative;
        }

        .footer-dropdown-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-bottom: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-dropdown-toggle:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .footer-dropdown-toggle i {
            margin-left: 8px;
            transition: transform 0.3s ease;
        }

        .footer-dropdown:hover .footer-dropdown-toggle i {
            transform: rotate(180deg);
        }

        .footer-dropdown-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            list-style: none;
            padding: 0;
            margin: 0;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .footer-dropdown:hover .footer-dropdown-menu {
            max-height: 300px;
        }

        .footer-dropdown-menu li {
            margin: 0;
            padding: 0;
        }

        .footer-dropdown-menu a {
            display: block;
            padding: 8px 12px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .footer-dropdown-menu a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border-left: 3px solid var(--accent-color);
            padding-left: 15px;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <div class="nav-container">
                    <button class="mobile-menu-btn" aria-label="Toggle menu"><i class="fas fa-bars"></i></button>
                    <div class="logo">
                        <img src="../images/Eden logo PNG.png" alt="EDEN Pharmaceuticals" />
                    </div>
                    <ul class="nav-links">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../about.html">About</a></li>
                        <li><a href="../plant.html">Our Plant</a></li>
                        <li class="active dropdown">
                            <a href="../products.html">Products</a>
                            <ul class="dropdown-menu">
                                <li><a href="01.html">Anti-Cold, Anti-Histaminic and Anti-Allergic</a></li>
                                <li><a href="02.html">Digestive Preparations and Antacids</a></li>
                                <li><a href="03.html">Ayurvedic Products</a></li>
                                <li><a href="04.html">External Preparations</a></li>
                                <li><a href="05.html">Injectables</a></li>
                                <li><a href="06.html">Nutritional Supplements and Haematinics</a></li>
                                <li><a href="07.html">Antibiotics</a></li>
                                <li><a href="08.html">Analgesic, Anti-Inflammatory and Anti-Arthritis</a></li>
                                <li><a href="09.html">Other Products</a></li>
                            </ul>
                        </li>
                        <li><a class="contact-btn" href="../contact.html">Contact Us</a></li>
                    </ul>
                </div>
            </nav>
        </header>

        <main>
            <!-- Hero Banner Section -->
            <section class="products-hero">
                <div class="products-hero-content">
                    <h1>Anti-Cold, Anti-Histaminic and Anti-Allergic</h1>
                    <p>High-quality pharmaceutical products for better health</p>
                </div>
            </section>

            <!-- Product Gallery Section -->
            <section class="products-gallery">
                <div class="container">
                    <div class="section-header">
                        <h2>Pharmaceutical <span class="highlight-text">Products</span></h2>
                        <p class="section-subtitle">Browse our comprehensive range of high-quality medications</p>
                    </div>

                    <div class="product-grid">
                        <!-- Product 1 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="eager" alt="Paracetamol Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Paracetamol</h3>
                                    <p>500mg Pain Relief Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 2 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="eager" alt="Ibuprofen Capsules">
                                </div>
                                <div class="product-content">
                                    <h3>Ibuprofen</h3>
                                    <p>400mg Anti-inflammatory</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 3 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Amoxicillin Capsules">
                                </div>
                                <div class="product-content">
                                    <h3>Amoxicillin</h3>
                                    <p>500mg Antibiotic Capsules</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 4 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1516815231353-1c8fab6a1041?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Cetirizine Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Cetirizine</h3>
                                    <p>10mg Allergy Relief Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 5 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Azithromycin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Azithromycin</h3>
                                    <p>250mg Antibiotic Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 6 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1510626176961-4b57d4fbad04?ixlib=rb-4.0.3" alt="Metformin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Metformin</h3>
                                    <p>500mg Diabetes Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 7 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?ixlib=rb-4.0.3" alt="Loratadine Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Loratadine</h3>
                                    <p>10mg Antihistamine Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 8 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1518717758536-85ae29035b6d?ixlib=rb-4.0.3" alt="Omeprazole Capsules">
                                </div>
                                <div class="product-content">
                                    <h3>Omeprazole</h3>
                                    <p>20mg Acid Reducer Capsules</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 9 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3" alt="Aspirin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Aspirin</h3>
                                    <p>75mg Heart Health Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 10 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?ixlib=rb-4.0.3" alt="Simvastatin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Simvastatin</h3>
                                    <p>20mg Cholesterol Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 11 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1461009312844-e80697a6a922?ixlib=rb-4.0.3" alt="Atorvastatin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Atorvastatin</h3>
                                    <p>10mg Cholesterol Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 12 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1468581264429-2548ef9eb732?ixlib=rb-4.0.3" alt="Pantoprazole Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Pantoprazole</h3>
                                    <p>40mg Acid Reducer Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 13 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?ixlib=rb-4.0.3" alt="Levocetirizine Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Levocetirizine</h3>
                                    <p>5mg Allergy Relief Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 14 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1518717758536-85ae29035b6d?ixlib=rb-4.0.3" alt="Doxycycline Capsules">
                                </div>
                                <div class="product-content">
                                    <h3>Doxycycline</h3>
                                    <p>100mg Antibiotic Capsules</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 15 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?ixlib=rb-4.0.3" alt="Clarithromycin Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Clarithromycin</h3>
                                    <p>500mg Antibiotic Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 16 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1510626176961-4b57d4fbad04?ixlib=rb-4.0.3" alt="Amlodipine Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Amlodipine</h3>
                                    <p>5mg Blood Pressure Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 17 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1468581264429-2548ef9eb732?ixlib=rb-4.0.3" alt="Rabeprazole Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Rabeprazole</h3>
                                    <p>20mg Acid Reducer Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 18 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1516815231353-1c8fab6a1041?ixlib=rb-4.0.3" alt="Montelukast Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Montelukast</h3>
                                    <p>10mg Asthma Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 19 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?ixlib=rb-4.0.3" alt="Losartan Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Losartan</h3>
                                    <p>50mg Blood Pressure Tablets</p>

                                </div>
                            </div>
                        </div>
                        <!-- Product 20 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1550572017-edd951b55104?ixlib=rb-4.0.3" alt="Naproxen Tablets">
                                </div>
                                <div class="product-content">
                                    <h3>Naproxen</h3>
                                    <p>250mg Pain Relief Tablets</p>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Download Section -->
            <section class="download-section">
                <div class="container">
                    <div class="download-content">
                        <h2>Product Information</h2>
                        <p>Download our complete product list with detailed specifications and pricing information.</p>
                        <a href="product-list.pdf" class="download-btn" download="EDKEM-Product-List.pdf">
                            <i class="fas fa-download"></i> Download Product List (PDF)
                        </a>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer id="contact">
            <div class="footer-content">
                <div class="footer-nav">
                    <div class="footer-col">
                        <h3>Map</h3>
                        <!-- Map will be added here -->
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3672.832211154431!2d72.5360038250273!3d22.993196717424333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e853ed21ede61%3A0xbbafb667025fe1b1!2sSAHAJ%20SOLITAIRE%2C%20Vishala%2C%20Ahmedabad%2C%20Gujarat%20380007!5e0!3m2!1sen!2sin!4v1746958440405!5m2!1sen!2sin" width="300" height="200" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                    <div class="footer-col">
                        <h3>Quick Links</h3>
                        <ul>
                            <li><a href="../index.html">Home</a></li>
                            <li><a href="../about.html">About</a></li>
                            <li><a href="../plant.html">Our Plant</a></li>
                            <li><a href="../products.html">Products</a></li>
                            <li><a href="../contact.html">Contact Us</a></li>
                        </ul>
                    </div>
                    <div class="footer-col">
                        <h3>Our Office</h3>
                        <p>301, Abhishree Complex, Opp Star Bazar</p>
                        <p>Satellite Road, Ahmedabad 12345</p>
                        <p>Phone: +91 9824023088</p>
                        <p>Mobile: +91 7016386329</p>
                        <p>Email: <EMAIL></p>
                    </div>
                    <div class="footer-col">
                        <h3>Business Hours</h3>
                        <p>Monday - Saturday: 9:00 AM - 5:00 PM</p>
                        <p>Sunday: Closed</p>
                        <p>We respond to emails within 24 hours during business days.</p>
                    </div>
                </div>
                <div class="copyright">
                    <p>Copyright © 2025 EDEN. All rights reserved.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </footer>

        <a href="#" class="back-to-top" aria-label="Back to top">
            <i class="fas fa-arrow-up"></i>
        </a>
    </div>

    <!-- Lightbox Container -->
    <div class="lightbox" id="productLightbox">
        <div class="lightbox-content">
            <button class="lightbox-close" id="lightboxClose"><i class="fas fa-times"></i></button>
            <img src="" alt="" class="lightbox-image" id="lightboxImage">
            <div class="lightbox-caption" id="lightboxCaption"></div>
        </div>
    </div>

    <!-- Optimized JavaScript -->
    <script src="../js/optimized.js" defer></script>

    <!-- Lightbox JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize lightbox functionality
            initLightbox();
        });

        function initLightbox() {
            const productImages = document.querySelectorAll('.product-image img');
            const lightbox = document.getElementById('productLightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxCaption = document.getElementById('lightboxCaption');
            const lightboxClose = document.getElementById('lightboxClose');

            // Touch support variables
            let touchStartY = 0;
            let touchEndY = 0;
            let touchStartX = 0;
            let touchEndX = 0;

            // Add click event to all product images
            productImages.forEach(image => {
                image.addEventListener('click', function() {
                    // Get the high-resolution image URL (remove size constraints)
                    let fullSizeUrl = this.src.replace(/&w=\d+&q=\d+/, '');

                    // Set the image source and alt text
                    lightboxImage.src = fullSizeUrl;
                    lightboxImage.alt = this.alt;

                    // Set the caption text
                    const productTitle = this.closest('.product-card').querySelector('h3').textContent;
                    const productDesc = this.closest('.product-card').querySelector('p').textContent;
                    lightboxCaption.textContent = `${productTitle} - ${productDesc}`;

                    // Show the lightbox
                    lightbox.classList.add('active');

                    // Prevent scrolling on the body
                    document.body.style.overflow = 'hidden';

                    // Add viewport meta tag adjustment for mobile
                    if (window.innerWidth <= 768) {
                        const viewport = document.querySelector('meta[name=viewport]');
                        if (viewport) {
                            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no');
                        }
                    }
                });
            });

            // Close lightbox when clicking the close button
            lightboxClose.addEventListener('click', closeLightbox);

            // Close lightbox when clicking outside the image
            lightbox.addEventListener('click', function(e) {
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });

            // Close lightbox when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                    closeLightbox();
                }
            });

            // Touch events for mobile swipe to close
            lightbox.addEventListener('touchstart', function(e) {
                if (lightbox.classList.contains('active')) {
                    touchStartY = e.changedTouches[0].screenY;
                    touchStartX = e.changedTouches[0].screenX;
                }
            }, { passive: true });

            lightbox.addEventListener('touchend', function(e) {
                if (lightbox.classList.contains('active')) {
                    touchEndY = e.changedTouches[0].screenY;
                    touchEndX = e.changedTouches[0].screenX;

                    const swipeThreshold = 100;
                    const verticalSwipe = touchStartY - touchEndY;
                    const horizontalSwipe = Math.abs(touchStartX - touchEndX);

                    // Close on upward swipe or significant horizontal swipe
                    if (verticalSwipe > swipeThreshold || horizontalSwipe > swipeThreshold) {
                        closeLightbox();
                    }
                }
            }, { passive: true });

            // Prevent zoom on double tap for lightbox image
            lightboxImage.addEventListener('touchend', function(e) {
                e.preventDefault();
            });

            function closeLightbox() {
                lightbox.classList.remove('active');

                // Re-enable scrolling on the body
                document.body.style.overflow = '';

                // Reset viewport meta tag for mobile
                if (window.innerWidth <= 768) {
                    const viewport = document.querySelector('meta[name=viewport]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                    }
                }

                // Clear the image source after transition (for better memory management)
                setTimeout(() => {
                    if (!lightbox.classList.contains('active')) {
                        lightboxImage.src = '';
                    }
                }, 300);
            }
        }
    </script>
</body>
</html>